import React, { useEffect, useState } from "react"
import "./style.scss"
import { useTranslation } from "react-i18next"
import coins from "../../../images/icons/cashback-coins.svg"
import yellowHeart from "../../../images/icons/yellow_Heart.svg"
import user from "../../../images/icons/user.svg"
import UserAvatar from "../../../components/UserAvatar"
import orgstar from "../../../images/icons/org-star.svg"
import Power from '../../../images/icons/powered-by.svg'
import { ReactComponent as Email } from '../../../images/icons/postPurchaseSignup/Union.svg'
import { ReactComponent as Promo } from '../../../images/icons/postPurchaseSignup/Promo.svg'
import { ReactComponent as Lock } from '../../../images/icons/postPurchaseSignup/light.svg'
import { Eye, EyeSlash, Val<PERSON><PERSON><PERSON><PERSON> } from "../../../components/Icons/arrow-right"
import kwik from "../../../images/icons/Kwik.svg"
import { useHistory } from "react-router-dom"
import { checkEmailUnique4NormalEnrol, getRewardsInfo, sendValidateEmailApp } from "../../../requests/register"
import { Checkbox, Modal, Toast } from "antd-mobile"
import sessionCache from "../../../utils/sessionCache"
import LoginModal from "../step1/loginModal"
import { Form, Input, Tooltip, message } from "antd"
import PasswordFormatTooltip from "../../../components/PasswordFormatTooltip"
import { PASSWORD_PATTERN } from "../../../config/webConfig"
import RewardsHistory from "./RewardsHistory"
import PhoneInput from "react-phone-number-input"

const mAlert = Modal.alert

const PostPurchaseSignup = () => {
    const { t } = useTranslation()
    const history = useHistory()
    const initialValues = {
        emailAddress: '',
        username: '',
        password: '',
        confirmPassword: '',
    }
    const [form] = Form.useForm()
    const { setFieldValue, setFieldsValue, getFieldsValue, getFieldValue, getFieldError, isFieldTouched } = form
    const [formErrors, setFormErrors] = useState({})
    const [userInfo, setUserInfo] = useState({})
    const [rewards, setRewards] = useState({})
    const [showForgetPWD, setShowForgetPWD] = useState(false)
    const [showLoginModal, setShowLoginModal] = useState(false)
    const [fields, setFields] = useState({})
    const [passwordtype, setPasswordType] = useState("password")
    const [confirmPasswordtype, setconfirmPasswordType] = useState("password")
    const [hasErrors, setHasErrors] = useState({
        emailAddress: false,
        username: false,
        password: false,
        confirmPassword: false,
    })
    const [confirmPasswordVerify, setConfirmPasswordVerify] = useState()
    const [agree, setAgree] = useState(false)
    const [isSubmit, setIsSubmit] = useState(false)
    const urlEmail = new URLSearchParams(history.location.search).get("email")
    const email = urlEmail ? urlEmail.replace(/%/g, "+") : ""

    const getRewards = (custId) => {
        let data = {
            custId,
        }
        Toast.loading("Loading...", 600)
        getRewardsInfo(data).then((resp) => {
            Toast.hide()
            if (resp.data.errorCode === "200") {
                setRewards(resp.data)
            } else {
            }
        })
    }

    const sendCodeToEmail = (email, cellphone, password, username, countryCode, displayName) => {
        
        let data = {
            email: email.trim(),
            displayName: displayName.trim(),
            languageId: sessionCache().getItem("language"),
        }
        Toast.loading("Loading...", 600)
        sendValidateEmailApp(data).then((res) => {
            Toast.hide()
            if (res.data.errorCode === "200") {
                const newInfo = { ...userInfo, password, username, cellphone, countryCode, email }
                sessionStorage.setItem(
                    "registerUserInfo",
                    JSON.stringify(newInfo)
                )
                mAlert(
                    "",
                    <div>
                        <div>{t("VIEWS_REGISTER_1_TIPS_DIGIT_SENT")} </div>
                        <div style={{ wordBreak: "break-word" }}>{email}</div>
                        <div
                            dangerouslySetInnerHTML={{
                                __html: `<style>
                  .am-modal-transparent{
                    width: 21rem;
                  }
                <style>`,
                            }}
                        ></div>
                    </div>,
                    [
                        {
                            text: t("VIEWS_REGISTER_1_BUTTON_OK"),
                            onPress: () => {
                                if (email) {
                                    history.push(
                                        "/register/step2?email=" +
                                        getFieldValue("emailAddress")
                                    )
                                } else {
                                    history.push("/register/step2")
                                }
                            },
                        },
                    ]
                )
            } else {
                Toast.info(res.data.errorMessage, 2)
            }
        })
    }

    const validatePassword = (e) => {
        const value = e.target.value.trim()
        let password = getFieldValue("password")
        if (value === password) {
            // callback();
            setFormErrors({
                ...formErrors,
                confirmPassword: false,
            })
        } else {
            // callback(this.props.t('VIEWS_REGISTER_3_TIPS_MATCH'));
            setFormErrors({
                ...formErrors,
                confirmPassword: {
                    errors: [{ message: t("VIEWS_REGISTER_3_TIPS_MATCH") }],
                },
            })
        }
    }

    const usernameEmailBlurInit = (type, value) => {
        if (value === "") {
            return
        }
        let data = {}
        if (type === 1) {
            data.username = value.trim()
        } else if (type === 2) {
            let valueTrim = value.trim()
            data.email = valueTrim
        } else if (type === 3) {
            data.cellPhone = value
        }
        Toast.loading("Loading...", 600)
        checkEmailUnique4NormalEnrol(data).then((res) => {
            Toast.hide()
            if (res.data.errorCode === "200") {
                if (type === 1) {
                    if (res.data.response.status !== 1) {
                        setFormErrors({
                            ...formErrors,
                            username: {
                                errors: [
                                    {
                                        message: t(
                                            "VIEWS_REGISTER_1_TIPS_USERNAME_REGISTERED"
                                        ),
                                    },
                                ],
                            },
                        })
                    } else {
                        setFormErrors({
                            ...formErrors,
                            username: false,
                        })
                    }
                } else if (type === 2) {
                    let status = res.data.response.status
                    if (status !== 3) {
                        setFormErrors({
                            ...formErrors,
                            emailAddress: false,
                        })
                    }
                    if (status === 1) {
                        sessionCache().removeItem("registerUserInfo")
                        setShowForgetPWD(false)
                        //  this.setState({ hasData: false, showForgetPWD: false })
                    } else if (status === 2) {
                        let response = res.data.response
                        const { enrolment, ...otherData } = response
                        const { applicantFirstNameRoman, ...otherenrolment } = enrolment
                        setUserInfo({
                            ...otherData,
                            ...otherenrolment,
                            applicantFirstNameRoman: applicantFirstNameRoman ?? '',
                        })
                        getRewards(response.custId)
                        setShowForgetPWD(false)

                    } else if (status === 3) {
                        setShowForgetPWD(true)
                        setShowLoginModal(true)
                        getRewards(res.data.response.custId)
                        setFields((prev) => ({ ...prev, loginEmail: value }))
                        // this.setState({
                        //     hasData: false,
                        //     showForgetPWD: true,
                        //     loginEmail: value,
                        //     showLoginModal: true,
                        // })
                    }
                } else if (type === 3) {
                    if (res.data.response.status !== 1) {
                        setFormErrors({
                            ...formErrors,
                            cellphone: {
                                errors: [{ message: t("PHONE_HAS_REGISTERED") }],
                            },
                        })
                    } else {
                        setFormErrors({
                            ...formErrors,
                            cellphone: false,
                        })
                    }
                }
            } else {
                //Toast.info(res.data.errorMessage, 2);
            }
        })
    }

    const usernameEmailBlur = (type, e) => {
        if (showLoginModal) {
            return
        }
        let value = e.target.value.trim()
        if (type == 2) {
            if (!value) {
                setFormErrors((prev) => ({
                    ...prev, emailAddress: {
                        errors: [
                            { message: t("SETTING_ACCOUNT_EMAIL_IS_REQUIRED") },
                        ],
                    },
                }))
                return
            }
            var pattern =
                /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            var flag = pattern.test(value)
            if (!flag) {
                setFormErrors((prev) => ({
                    ...prev, emailAddress: {
                        errors: [
                            { message: t("INVALID_EMAIL") },
                        ],
                    },
                }))
                return
            } else {
                setFormErrors((prev) => ({
                    ...prev, emailAddress: false,
                }))
            }
        } else if (type == 1) {
            var pattern = /[^a-zA-Z0-9\.\_\-]/g
            var flag = pattern.test(value)
            if (flag) {
                let message = t("USER_REGISTER_INVLID_INPUT_CHARACTERS")
                if (value.indexOf(" ") > -1) {
                    message = t("SPACES_ARE_NOT_ALLOWED_IN_USERNAME")
                }
                setFormErrors((prev) => ({
                    ...prev, username: {
                        errors: [
                            {
                                message,
                            },
                        ],
                    },
                }))
                return
            } else {
                setFormErrors((prev) => ({
                    ...prev, username: false,
                }))
            }
        }
        usernameEmailBlurInit(type, value)
    }

    const isError = (field) => {
        if (isFieldTouched(field) && getFieldError(field).length) {
            return true
        }
        else if (isSubmit && getFieldError(field).length) {
            return true
        }
        return false
    }

    const onFinish = (values) => {
        setConfirmPasswordVerify(true)
        if (!agree) {
            mAlert(
                "",
                <div
                    dangerouslySetInnerHTML={{
                        __html: t("CHECKOUT_BY_CLICKING_CONFIRM_MESSAGE"),
                    }}
                ></div>,
                [{ text: t("OK") }]
            )
            return
        }
        if (values.password !== values.confirmPassword) {
            mAlert(
                "",
                <div
                    dangerouslySetInnerHTML={{
                        __html: t("VIEWS_REGISTER_3_TIPS_MATCH"),
                    }}
                ></div>,
                [{ text: t("OK") }]
            )
            return
        }
        let pattern = PASSWORD_PATTERN
        let flag = pattern.test(values.password)
        if (!flag) {
            setFormErrors({
                ...formErrors,
                password: t("VIEWS_REGISTER_3_TOAST_PATTERN"),
            })
            Toast.info(t("VIEWS_REGISTER_3_TOAST_PATTERN"), 2)
            return
        }
        //run register function

        setUserInfo((prev) => ({ ...prev, email: values.emailAddress, password: values.password, username: values.username , cellPhone: values.cellphone , countryCode: values.countryCode }))
        sendCodeToEmail(values.emailAddress,values.cellphone, values.password, values.username, values.countryCode ,userInfo?.applicantFirstNameRoman ?? userInfo?.applicantLastNameRoman + " " + userInfo?.enrolment?.applicantLastNameRoman)
    }

    useEffect(() => {
        if (email) {
            setFieldValue('emailAddress', email)
            usernameEmailBlurInit(2, email)
        }
    }, [])

    const onFinishFailed = (values) => {
        setIsSubmit(true)
        message.error('Unable to Save.Please correct fields in red.')
    }

    return (
        <div className="post-purchase-user-wrapper">
            <div className="post-purchase-content">
                <div className="post-purchase-left">
                    <div className="flex justify-center">
                        <img src="https://cms.kwik.dev/images/campaigns/Young Living.jpg" className="mobile-image" />
                    </div>
                    <RewardsHistory rewards={rewards} />
                </div>
                <div className="post-purchase-right">
                    <Form form={form}
                        onFinish={onFinish}
                        initialValues={initialValues}
                        onError={onFinishFailed}
                        onFinishFailed={onFinishFailed}
                        onValuesChange={(values) => {
                            setTimeout(() => setHasErrors((prev) => ({ ...prev, [Object.keys(values)[0]]: isError(Object.keys(values)[0]) })))
                        }
                        }
                        autoComplete="off">
                        <div className="post-purchase-right-content">
                            <div className="claim-reward">{t("CLAIM_YOUR_REWARDS")}</div>
                            <div className="claim-reward-subtext">{t("ENTER_PASSWORD_TO_CLAIM")}</div>
                            <div className="break-line" />

                            <div>
                                <div className={`input ${!!email ? 'disable' : ''} ${isError('emailAddress') && 'margin-bottom-35'}`}>
                                    <Email />
                                    <Form.Item name='emailAddress' rules={[
                                        {
                                            required: true,
                                            message: 'Please input email',
                                        },
                                    ]}>
                                        <Input
                                            type="email"
                                            placeholder={t("VIEWS_REGISTER_1_LABEL_EMAIL_ADDRESS")}
                                            value={getFieldValue('emailAddress')}
                                            disabled={!!email}
                                            onBlur={(e) => usernameEmailBlur(2, e)}
                                        />
                                    </Form.Item>
                                    {getFieldValue("emailAddress") &&
                                        !formErrors?.emailAddress && (
                                            <span className="success">
                                                <ValidCheck />
                                            </span>)
                                    }
                                </div>
                                {(!getFieldError('emailAddress').length && formErrors?.emailAddress?.errors && formErrors?.emailAddress?.errors.length > 0 && getFieldError("emailAddress").length < 0)
                                    && <p className="error">{formErrors?.emailAddress?.errors[0].message}</p>
                                }

                                <div className={`input ${isError('cellphone') && 'margin-bottom-35'}`}>
                                    <Form.Item name='cellphone' rules={[
                                        {
                                            required: true,
                                            message: 'Please input Phone Number',
                                        },
                                    ]}>
                                        <PhoneInput
                                            className={`textInput`}
                                            autoComplete="off"
                                            defaultCountry="US"
                                            value={getFieldValue('cellphone')}
                                            placeholder={t(
                                                "VIEWS_REGISTER_1_PLACEHOLDER_PHONE"
                                            )}
                                        />
                                    </Form.Item>
                                    {getFieldValue("cellphone") &&
                                        !formErrors?.cellphone && (
                                            <span className="success">
                                                <ValidCheck />
                                            </span>)
                                    }
                                </div>
                                {(!getFieldError('cellphone').length && formErrors?.cellphone?.errors && formErrors?.cellphone?.errors.length > 0 && getFieldError("cellphone").length < 0)
                                    && <p className="error">{formErrors?.cellphone?.errors[0].message}</p>
                                }

                                <div className={`input username ${isError('username') ? 'margin-bottom-35' : ''}`}>
                                    <Promo />
                                    <Form.Item name='username'
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please input Promocode',
                                            },
                                        ]}
                                    >
                                        <Input
                                            placeholder={t("SET_YOUR_OWN_PROMO")}
                                            id='new-promo-username'
                                            autoComplete='new-promo-username'
                                            value={getFieldValue('username') ?? ''}
                                            onBlur={(e) => usernameEmailBlur(1, e)}

                                            suffix={
                                                <Tooltip title={t('POST_REGISTER_PROMO_CODE_TOOLTIP')}>
                                                    <span className="iIcon">i</span>
                                                </Tooltip>
                                            }
                                        />
                                    </Form.Item>
                                </div>

                                {(!getFieldError('username').length && formErrors?.username?.errors && formErrors?.username?.errors.length > 0)
                                    && <p className="error">{formErrors?.username?.errors[0].message}</p>
                                }

                                <div className={`input ${isError('password') && 'margin-bottom-35'}`}>
                                    <Lock />
                                    <Form.Item
                                        name='password'
                                        rules={[
                                            {
                                                required: true,
                                                message: 'Please input Password',
                                            },
                                        ]}
                                    >
                                        <Input
                                            type={passwordtype}
                                            placeholder={t("CREATE_PASSWORD_TITLE")}
                                            id="post-purchse-password"
                                            autocomplete="new-password"
                                            onFocus={(event) => { event.target.removeAttribute('readonly') }}
                                            readonly="readonly"
                                            value={getFieldValue('password') ?? ''}
                                            onBlur={(e) => {
                                                const value = e.target.value.trim()
                                                if (value) {
                                                    setFormErrors({
                                                        ...formErrors,
                                                        password: false,
                                                    },
                                                    )
                                                }
                                                // this.setState({
                                                //   passwordTooltipVisible: false,
                                                // })
                                            }}
                                        />
                                    </Form.Item>
                                    <span
                                        onClick={() =>
                                            setPasswordType(passwordtype === "password" ? "text" : "password")
                                        }
                                    >
                                        {passwordtype === "password" ? <EyeSlash /> : <Eye />}
                                    </span>
                                </div>

                                {(!getFieldError('password').length && formErrors?.password?.errors && formErrors?.password?.errors.length > 0)
                                    && <p className="error">{formErrors?.password?.errors[0].message}</p>
                                }

                                <PasswordFormatTooltip
                                    value={getFieldValue('password')}
                                    verify={confirmPasswordVerify}
                                >

                                    <div className={`input ${isError('confirmPassword') && 'margin-bottom-35'}`}>
                                        <Lock />
                                        <Form.Item
                                            name='confirmPassword'
                                            rules={[
                                                {
                                                    required: true,
                                                    message: 'Please input Confirm Password',
                                                },
                                            ]}
                                            validateStatus={getFieldError('confirmPassword').length ? "error" : ""}
                                        >
                                            <Input
                                                type={confirmPasswordtype}
                                                placeholder={t("VIEWS_REGISTER_3_CONFIRM_LABEL")}
                                                id="post-purchse-new-confirm-password"
                                                autoComplete="post-purchse-new-confirm-password"
                                                value={getFieldValue('confirmPassword') ?? ''}
                                                onBlur={(e) => validatePassword(e)}
                                            />
                                        </Form.Item>
                                        <span
                                            onClick={() =>
                                                setconfirmPasswordType(confirmPasswordtype === "password" ? "text" : "password")
                                            }
                                        >
                                            {confirmPasswordtype === "password" ? <EyeSlash /> : <Eye />}
                                        </span>
                                    </div>
                                    {(!getFieldError('confirmPassword').length && formErrors?.confirmPassword?.errors && formErrors?.confirmPassword?.errors.length > 0)
                                        && <p className="error">{formErrors?.confirmPassword?.errors[0].message}</p>
                                    }
                                </PasswordFormatTooltip>

                                <div className="forgot" style={{ display: "block" }}>
                                    <Checkbox
                                        className="agree"
                                        checked={agree}
                                        onChange={(e) => setAgree(e.target.checked)}
                                        style={{ borderRadius: '5px' }}
                                    >
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: t("ERC__CLICKING_CONFIRM_MESSAGE"),
                                            }}
                                        ></div>
                                    </Checkbox>
                                </div>

                                <div>
                                    <button className="submit-btn" htmlType='submit' type='submit'>{t("SS_CREATE_HOME")}</button>
                                </div>

                                <div className="claim-reward-subtext">
                                    {t("REGISTER_ALREADY_A_MEMBER")}{" "}
                                    <a href="#" onClick={(e) => {
                                        e.preventDefault()
                                        e.stopPropagation()
                                        setShowForgetPWD(true)
                                        setShowLoginModal(true)
                                        setFields((prev) => ({ ...prev, loginEmail: email }))
                                    }}>{t("REGISTER_SIGN_IN")}</a>
                                </div>

                                <img src={kwik} className="bottom-logo" />
                            </div>
                        </div>
                    </Form>
                </div>
            </div >
            <LoginModal
                loginEmail={fields?.loginEmail}
                showLoginModal={showLoginModal}
                setShowLoginModal={(value) => setShowLoginModal(value)}
            />

        </div >
    )
}

export default PostPurchaseSignup