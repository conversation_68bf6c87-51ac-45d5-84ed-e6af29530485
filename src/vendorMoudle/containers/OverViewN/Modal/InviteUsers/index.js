import React, { useState, useRef, useEffect } from "react";
import "./style.scss";
import "../public.scss";
import {
  Button,
  Checkbox,
  Divider,
  Form,
  Modal,
  Radio,
  Select,
  Upload,
  Switch,
  Tooltip,
  message
} from "antd";
import { useTranslation } from "react-i18next";
import { Toast } from "antd-mobile";
import { checkEmailUnique4NormalEnrol } from "../../../../../requests/register"
import { getPromocodeByEmail } from "../../../../requests/dashboard"
function InviteUsers({
  showInviteUsesModal,
  modalClose,
  submitHandle,
  submitData,
  setSubmitData,
  prodImageBeforeUpload,
  statusList
}) {
  const { t } = useTranslation();
  const { Dragger } = Upload;

  const [isUnique,setIsUnique] = useState(true);
  const promoCodeInput = useRef(null);
  
  const promoCodeHandleChange = e => {
    let data = {
      username: e.target.value
    }
    Toast.loading("Loading...", 600);
    checkEmailUnique4NormalEnrol(data).then((res) => {
        Toast.hide();
        if (res.data.errorCode === "200") {
          let status = res.data.response.status;
          if (status === 1) {
            setSubmitData({ ...submitData, promoCode: e?.target?.value })
          }else {
            Toast.info(t("VIEWS_REGISTER_1_TIPS_USERNAME_REGISTERED"));
            promoCodeInput.current.focus();
          }
      }
    })
    
  }
  const emailsHandleChange = value => {
    if (value.length > 0) {
      setSubmitData({ ...submitData, emails: value});
      checkEmailUniqueBlur(value[value.length - 1])   
    } else {
      setSubmitData({ ...submitData, emails: [], promoCode: '' });
      setIsUnique(true)
    }
    // if (value.length <= 1) {
    //   // setTags(value); // Update tags only if within the limit
    //   setSubmitData({ ...submitData, emails: value });
    // }
  };

  const changeSwitch = e => {
    setSubmitData({ ...submitData, isSendEmail: e });
  };

  const statusChange = e => {
    setSubmitData({ ...submitData, status: e.target.value });
  };
  const campaignsSelectChange = (e, id) => {
    const newCampaigns = submitData.campaigns.map(item => {
      if (item.id === id) {
        item.selected = e.target.checked;
      }
      return item;
    });
    setSubmitData({ ...submitData, campaigns: newCampaigns });
  };

  const onCancelHandle = () => {
    modalClose();
  };

  const checkEmailUniqueBlur = (e) => {
    const email = e.trim();
    if (!email) {
      return;
    }
    let data = {
      email
    }
    Toast.loading("Loading...", 600);          
    getPromocodeByEmail(data).then((res) => {
      Toast.hide();            
      if (res.data.errorCode === "200" && res.data.data) {
        setIsUnique(false)
        setSubmitData((prev) => ({ ...prev, promoCode: res.data.data }));
        if(res.data.data && res.data.data !== ''){
          message.info('This User Already Has a Promo Code')
        }
      }
    })
  }

  const onInviteHandle = () => {
    console.log("sub", submitData);
    
    // if (submitData.emails.length === 0) {
    //   Toast.info(t("AMBASSADORS_TOAST_ADD_EMAIL"), 2);
    // } else if(submitData?.promoCode && submitData?.promoCode.indexOf(" ") > -1){
    //   console.log("in validation");
      
    //   Toast.info(t("SPACES_ARE_NOT_ALLOWED_IN_PROMO"), 2);
    // }
    //  else {
    //   submitHandle();
    // }
  };

  useEffect(() => {
    console.log("submitData", submitData);
    
  }, [submitData])

  return (
    <div>
      <Modal
        visible={showInviteUsesModal}
        onCancel={modalClose}
        footer={null}
        closable={false}
        maskClosable={true}
        wrapClassName="invite-users-modal"
        width={768}
      >
        <div className="invite-users-title">
          {t("AMBASSADORS_BUTTON_ADD_AMB")}
        </div>
        <div className="invite-users-line" />
        <div className="invite-users-modal-body">
          <div className="invite-users-to">To</div>
          <div className="invite-users-email-select">
            <Select
              mode="tags"
              style={{
                width: "623px"
              }}
              placeholder="Enter Email"
              onChange={emailsHandleChange}
              value={submitData.emails}
              open={false}
            />
          </div>
          <div className="left-right-layout">
            {/* <div className="modal-public campaigns-name-title">
              {t("AMBASSADORS_TITLE_SELECT_CAMPAIGNS")}
            </div>
            <div className="modal-public campaigns-name-text">
              {t("AMBASSADORS_TITLE_CAMPAIGN_NAME")}
            </div>
            <div className="modal-public campaigns-item">
              {submitData.campaigns &&
                submitData.campaigns.map((item, index) => {
                  return (
                    <div
                      style={{ display: "flex", alignItems: "center" }}
                      key={item.id}
                    >
                      <Checkbox
                        style={{ minWidth: 200 }}
                        checked={item.selected}
                        onChange={e => {
                          campaignsSelectChange(e, item.id);
                        }}
                      >
                        {item.name}
                      </Checkbox>
                      <Checkbox
                        style={{ marginLeft: 20 }}
                        checked={item.inviteOption == 1}
                        onChange={e => {
                          const tempObj = Object.assign({}, submitData);
                          tempObj.campaigns.map(campaign => {
                            if (campaign.id == item.id) {
                              campaign.inviteOption = e.target.checked ? 1 : 0;
                            }
                          });
                          setSubmitData(tempObj);
                        }}
                      >
                        With 'Invite a Friend' Option
                      </Checkbox>
                      {index == 0 && (
                        <Tooltip
                          placement="right"
                          title="Checking this box allows your Ambassadors to invite others to join your campaign. More friends = more fun, right?"
                        >
                          <img
                            className="i_img"
                            src={require("../../../../images/product_i.png")}
                          />
                        </Tooltip>
                      )}
                    </div>
                  );
                })}
            </div> */}
            <div className="modal-public assign-a-status">
              {t("AMBASSADORS_BUTTON_ASSIGN_STATUS")}
            </div>
            <div className="modal-public status-item" style={{ marginTop: '12px'}}>
              <Radio.Group
                onChange={e => {
                  statusChange(e);
                }}
                value={submitData.status * 1}
              >
               
                {statusList && (
                <div className="status">
                  <div className="column">
                  {statusList.map(item => {
                      return (
                      <Radio key={item.id} value={item.id}>
                        {item.name}
                      </Radio>
                    );
                  }).slice(0,3)}
                  </div>
                 <div className="column">
                  {statusList.map(item => {                  
                    return (
                      <Radio key={item.id} value={item.id}>
                        {item.name}
                      </Radio>
                    );
                  }).slice(3)}
                  </div>
                  </div>)
                 }
              </Radio.Group>
            </div>
            
            <div className="invite-users-text">
              {t("AMB_PROMOCODE")}
            </div>
            <div className="invite-users-text">
              <input
                className="invite-users-text-border"
                type="text"
                maxLength={30}
                placeholder="Ex: johndoe25"
                value={submitData?.promoCode}
                disabled={!isUnique}
                onChange={(e) =>  setSubmitData({ ...submitData, promoCode: e.target.value })}
                onBlur={(e) => promoCodeHandleChange(e)}
                ref={promoCodeInput}
                />
            </div>

            {/* {!isUnique && (<div className="help-text" style={{ marginTop: '10px' }}>
                {t("USER_ALREADY_HAS_PROMOCODE")}
            </div>)} */}

            <div className="left-image-upload">
              <div className="general-form">
                <span className="upload-image-title">
                  {t("AMBASSADORS_TITLE_UPLOAD_DP")}
                </span>
                
                <Form>
                  {submitData.imageUrl && (
                    <Button
                      onClick={() => {
                        setSubmitData({ ...submitData, imageUrl: "" });
                      }}
                      className="black-btn"
                      style={{ marginBottom: '15px'}}
                      disabled={!isUnique}
                    >
                      {t("AMBASSADORS_BUTTON_REMOVE")}
                    </Button>
                  )}
                  <Form.Item className="card">
                    <Dragger
                      className="image-upload"
                      action=""
                      showUploadList={false}
                      beforeUpload={prodImageBeforeUpload}
                      disabled={!isUnique}
                    >
                      {submitData.imageUrl === "" ? (
                        <>
                          <img
                            src={require("../img/image-upload.svg")}
                            alt=""
                          />
                          <div className="image-upload-text1">
                            {t("AMBASSADORS_TITLE_UPLOAD_TEXT")}
                          </div>
                          <div className="image-upload-text2">
                            {t("AMBASSADORS_TITLE_DROP_TIPS")}
                          </div>
                        </>
                      ) : (
                        <img
                          src={submitData.imageUrl}
                          alt=""
                          style={{ maxWidth: "200px", maxHeight: "200px" }}
                        />
                      )}
                    </Dragger>
                  </Form.Item>
                  <div className="help-text">
                    {t("AMBASSADORS_TITLE_UPLOAD_SUGGEST")}
                  </div>
                </Form>
              </div>
            </div>
            <div className="modal-public campaigns-name-title email-title">
              {t("AMBASSADORS_TITLE_SEND_EMAIL_TO_AMB")}
            </div>
            <div className="modal-public campaigns-name-text email-message">
              {t("AMBASSADORS_MESSAGE_SEND_EMAIL_TO_AMB")}
            </div>
            <Switch
              onChange={changeSwitch}
              defaultChecked={true}
              checked={submitData.isSendEmail}
              className={
                submitData.isSendEmail ? "switchStyle on" : "switchStyle off"
              }
            />
            <div className={"modal-public buttons-area"}>
              <a onClick={onCancelHandle}>
                <div className={"button-cancel"}>
                  {t("AMBASSADORS_BUTTON_CANCEL")}
                </div>
              </a>
              <a onClick={onInviteHandle}>
                <div className={"button-copy"}>
                  <div className="button-name">
                    {/* <img src={require("../img/invite.svg")} /> */}
                    {t("VENDOR_SETTINGS_ADD_USERS")}
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
}

export default InviteUsers;
