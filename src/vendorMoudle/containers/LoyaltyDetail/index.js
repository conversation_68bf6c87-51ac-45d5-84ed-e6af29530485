import React, { useEffect, useRef, useState } from "react";
import TopToolbar from "../../components/OverView/TopToolbar";
import { Link, NavLink, useParams } from "react-router-dom";
import { Breadcrumbs } from "../Integrations/components/breadcrumbs";
import { StatisticsCard } from "../Integrations/components/statisticsCard";

import { ReactComponent as SalesIcon } from "../../../images/icons/stats/sales.svg";
import { ReactComponent as EarnedIcon } from "../../../images/icons/stats/earned.svg";
import { ReactComponent as EanrersIcon } from "../../../images/icons/stats/earners.svg";
import { ReactComponent as CustomersIcon } from "../../../images/icons/stats/customers.svg";
import { ReactComponent as RedeemedIcon } from "../../../images/icons/stats/redeemed.svg";
import { AmbassadorsTable } from "../Loyalty/Components/AmbassadorsTable";
import { TransactionsTable } from "../Loyalty/Components/TransactionsTable";
import {
  getLoyaltyPoint,
  getLoyaltyPointStatistics,
} from "../../requests/loyalty";
import Moment from "moment";
import PermissionsControl from "../../../components/PermissionsControl";

export function LoyaltyDetail(props) {
  const { t } = props;

  Moment.locale("en");

  // router param id
  const params = useParams();
  const { id } = params;

  const [currentVendorId, setCurrentVendorId] = useState(
    sessionStorage.getItem("loginType") === "0"
      ? sessionStorage.getItem("vendorAdminSelectVendorId")
      : sessionStorage.getItem("vendorId")
  );
  const [vendorList, setVendorList] = useState(
    sessionStorage.getItem("vendorList")
      ? JSON.parse(sessionStorage.getItem("vendorList"))
      : []
  );

  const [status, setStatus] = useState(false);
  const [currency, setCurrency] = useState("USD");

  const [pointName, setPointName] = useState("");
  const [pointIcon, setPointIcon] = useState("");

  const [totalSalesGenerated, setTotalSalesGenerated] = useState(0);
  const [totalSalesGeneratedGrowth, setTotalSalesGeneratedGrowth] = useState(0);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [totalCustomersGrowth, setTotalCustomersGrowth] = useState(0);
  const [activeEarners, setActiveEarners] = useState(0);
  const [activeEarnersGrowth, setActiveEarnersGrowth] = useState(0);
  const [pointsEarned, setPointsEarned] = useState(0);
  const [pointsEarnedGrowth, setPointsEarnedGrowth] = useState(0);
  const [pointsRedeemed, setPointsRedeemed] = useState(0);
  const [pointsRedeemedGrowth, setPointsRedeemedGrowth] = useState(0);

  const handleSearch = () => {};
  const changeLanguage = (country, lang) => {
    sessionStorage.setItem("language", lang);
    window.location.chooseLang(lang);
  };

  const currencyCodeToSymbol = (currencyCode) => {
    const currency = currencyCode.toUpperCase();
    switch (currency) {
      case "USD":
        return "$";
      case "EUR":
        return "€";
      case "CAD":
        return "C$";
      default:
        return "$";
    }
  };

  const numberFormat = (num, digits) => {
    const lookup = [
      { value: 1, symbol: "" },
      { value: 1e3, symbol: "k" },
      { value: 1e6, symbol: "M" },
      { value: 1e9, symbol: "G" },
      { value: 1e12, symbol: "T" },
      { value: 1e15, symbol: "P" },
      { value: 1e18, symbol: "E" },
    ];
    const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
    var item = lookup
      .slice()
      .reverse()
      .find(function (item) {
        return Math.abs(num) >= item.value;
      });
    return item
      ? (num / item.value).toFixed(digits).replace(rx, "$1") + item.symbol
      : "0";
  };

  useEffect(() => {
    if (id == "null" || id == "undefined" || !id) {
      return;
    }
    const data = {
      vendorId: currentVendorId,
      loyaltyPointId: id,
    };

    getLoyaltyPoint(data).then((res) => {
      if (res.data) {      
        sessionStorage.setItem("cpgnId", res.data.data.campaignId);
        setPointName(res.data.data.loyaltyPointName);
        setPointIcon(res.data.data.iconUrl);
        setStatus(res.data.data.status);
        setTotalSalesGenerated(res.data.data.statistics.totalSalesGenerated);
        setCurrency(res.data.data.statistics.currency);
        setTotalCustomers(res.data.data.statistics.customers);
        setActiveEarners(res.data.data.statistics.activeEarners);
        setPointsEarned(res.data.data.statistics.pointsEarned);
        setPointsRedeemed(res.data.data.statistics.pointsRedeemed);
      }
    });
  }, [id]);

  return (
    <div className="loyalty">
      <div className="w-content h-screen bg-gray-bg overflow-scroll flex flex-col items-center page">
        <TopToolbar
          handleSearch={handleSearch}
          setLanguage={changeLanguage}
          vendorList={vendorList}
          vendorId={currentVendorId}
          setVendorId={setCurrentVendorId}
        />

        <div className="p-14 w-full">
          <div className="flex w-full justify-between align-baselines">
            <Breadcrumbs
              breadcrumbs={[
                { name: "Loyalty", link: "/vendor/loyalty" },
                {
                  name: (
                    <div className="flex items-center">
                      <img
                        src={
                          pointIcon
                            ? pointIcon
                            : require("../../../images/icons/loyalty/4.svg")
                        }
                        alt="icon"
                        className="w-8 h-8 mr-2"
                      />
                      <span className="ml-1">{pointName}</span>
                    </div>
                  ),
                  link: `/vendor/loyalty/view/${id}`,
                },
              ]}
              large
            />
            <PermissionsControl name="Edit Loyalty">
              <Link to={`/vendor/loyalty/edit/${id}`} rc>
                <div className="button h-fit">Edit</div>
              </Link>
            </PermissionsControl>
          </div>

          <div className="card">
            <div className="card-header border-none !pb-0">
              <h3 class="text-2xl font-heading font-bold text-black-1">
                Point Overview
              </h3>
            </div>
            <div className="card-content">
              <div className="flex justify-between gap-3">
                <StatisticsCard
                  title="Total Sales Generated"
                  value={
                    currencyCodeToSymbol(currency) +
                    " " +
                    numberFormat(totalSalesGenerated, 1)
                  }
                  growth={numberFormat(totalSalesGeneratedGrowth, 1)}
                  icon={<SalesIcon />}
                />
                <StatisticsCard
                  title="Referrals"
                  value={numberFormat(totalCustomers, 1)}
                  growth={numberFormat(totalCustomersGrowth, 1)}
                  icon={<CustomersIcon />}
                />
                <StatisticsCard
                  title="Active Earners"
                  value={numberFormat(activeEarners, 1)}
                  growth={numberFormat(activeEarnersGrowth, 1)}
                  icon={<EanrersIcon />}
                />
                <StatisticsCard
                  title="Points Earned"
                  value={numberFormat(pointsEarned, 1)}
                  growth={numberFormat(pointsEarnedGrowth, 1)}
                  icon={<EarnedIcon />}
                />
                <StatisticsCard
                  title="Points Redeemed"
                  value={numberFormat(pointsRedeemed, 1)}
                  growth={numberFormat(pointsRedeemedGrowth, 1)}
                  icon={<RedeemedIcon />}
                />
              </div>
            </div>
          </div>

          <AmbassadorsTable />

          <TransactionsTable loyaltyPointId={id} vendorId={currentVendorId} />
        </div>
      </div>
    </div>
  );
}

LoyaltyDetail.propTypes = {};

export default LoyaltyDetail;
