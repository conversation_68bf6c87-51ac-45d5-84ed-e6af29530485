import React, { useEffect, useRef, useState } from "react";
import { DataTable } from "../../Integrations/components/datatable";
import { Button } from "react-bootstrap/lib/InputGroup";
import PopupModal from "../../Integrations/components/popupModal";
import Moment from "moment";
import { Input } from "../../Integrations/components/input";
import {
  addLpForCust,
  getLoyaltyPointCustomers,
  updateCustomerBalance,
} from "../../../requests/loyalty";
import { useParams } from "react-router-dom";
import { message } from "antd";
import UserAvatar from "../../../../components/UserAvatar"
import { getPromocodeByEmail } from "../../../requests/dashboard"
import { Toast } from "antd-mobile"

export function AmbassadorsTable() {
  Moment.locale("en");

  const params = useParams();
  const { id } = params;
  const [currentVendorId, setCurrentVendorId] = useState(
    sessionStorage.getItem("loginType") === "0"
      ? sessionStorage.getItem("vendorAdminSelectVendorId")
      : sessionStorage.getItem("vendorId")
  );
  const [vendorList, setVendorList] = useState(
    sessionStorage.getItem("vendorList")
      ? JSON.parse(sessionStorage.getItem("vendorList"))
      : []
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [searchFilter, setSearchFilter] = useState("");
  const [selection, setSelection] = useState([]);
  const [refresh, setRefresh] = useState(Math.random());
  const [balance, setBalance] = useState(0);
  const [msg, setMessage] = useState("");
  const perPage = 5;

  const [showModal, setShowModal] = useState(false);
  const [showAddPointsModal, setShowAddPointsModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [rawData, setRawData] = useState([]);
  const [total, setTotal] = useState(0);
  const [addPointsValue, setAddPointsValue] = useState(0);
  const [email, setEmail] = useState("");
  const [addPointsFname, setAddPointsFname] = useState("");
  const [addPointsLname, setAddPointsLname] = useState("");

  const [activeSort, setActiveSort] = useState({
    header: null,
    direction: null,
  });

  const sortHeaders = [
    "name",
    // "end_date",
    "points_earned",
    "points_redeemed",
    "balance",
    null,
  ];

  const headings = [
    "Name",
    // "Date",
    "Total Points Earned for LP",
    "Points spent",
    "Balance",
    "",
  ];


  const editBalanceMarkup = (custId) => {
    return (
      <>
        <div className="flex justify-between gap-2">
          <button
            className="button button--secondary button--sm !bg-gray-50 !text-gray-500"
            onClick={() => {
              setSelectedUser(
                rawData.find((item) => item.customerId === custId)
              );
              setShowModal(true);
            }}
            style={{ marginRight: '10px' }}
          >
            Edit Balance
          </button>
          <button
            className="button button--secondary button--sm !bg-gray-50 !text-gray-500"
            onClick={() => {
              setSelectedUser(
                rawData.find((item) => item.customerId === custId)
              );
              setShowAddPointsModal(true);
            }}
          >
            Add Points
          </button>
        </div>

      </>
    );
  };

  useEffect(() => {
    getLoyaltyPointCustomers({
      vendorId: currentVendorId,
      loyaltyPointId: id,
      limit: perPage,
      page: currentPage,
      q: searchFilter ? searchFilter : "",
      sortBy: activeSort.header,
      orderBy: activeSort.direction,
    })
      .then((res) => {
        setRawData(res.data.results);
        setTotal(res.data.count);
      })
      .catch((err) => {
        console.log(err);
      });
  }, [currentPage, searchFilter, activeSort, refresh]);

  const rows = rawData.map((item) => {
    return {
      id: item.customerId,
      columns: [
        <div className="flex items-center">
          {/* <img
            className="border-gray-200 border-[1px] rounded-full object-cover w-11 h-11"
            src={item.avatarUrl}
            alt=""
          /> */}
          <UserAvatar
            width={37}
            imageUrl={item.avatarUrl}
            displayName={item.customerName}
          />
          <div className="flex flex-col ml-3">
            <span className="text-black-1 font-heading-semi font-semibold text-base">
              {item.customerName}
            </span>
            <span className="text-black-1 font-heading-semi font-semibold text-sm opacity-50">
              {item.username}
            </span>
          </div>
        </div>,
        // Moment(item.registrationDate).format("MMMM Do, YYYY"),
        `${item.pointsEarned} Points`,
        `${item.pointsRedeemed} Points`,
        `${item.balance} Points`,
        editBalanceMarkup(item.customerId),
      ],
    };
  });

  const updateCustomerBalanceFunc = () => {
    if (balance == 0 || balance == "") {
      message.error("Please input correct points!");
      return;
    }
    updateCustomerBalance({
      vendorId: currentVendorId,
      loyaltyPointId: id,
      customerId: selectedUser.customerId,
      value: Math.abs(balance),
      operation: balance > 0 ? "add" : "subtract",
      message: msg,
    })
      .then((res) => {
        setShowModal(false);
        setMessage("");
        setBalance(0);
        setRefresh(Math.random());
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const addPointsForCustomerFunc = () => {
    if (addPointsValue <= 0 || addPointsValue == "") {
      message.error("Please input correct points!");
      return;
    }
    if(!sessionStorage.getItem("cpgnId")){
     return
    }
    addLpForCust({
      vendorId: currentVendorId,
      lpId: id,
      cpgnId: sessionStorage.getItem("cpgnId"),
      customerId: selectedUser.customerId,
      lp: Math.abs(addPointsValue),
      email: email,
      fname: addPointsFname,
      lname: addPointsLname,
    })
      .then((res) => {
        message.success("Points added successfully!");
        setShowAddPointsModal(false);
        setAddPointsValue(0);
        setEmail("");
        setAddPointsFname("");
        setAddPointsLname("");
        setRefresh(Math.random());
      })
      .catch((err) => {
        console.log(err);
        message.error("Failed to add points. Please try again.");
      });
  };

   const checkEmailUniqueBlur = (e) => {
     const email = e.target.value.trim();
     if (!email) {
       return;
     }
     let data = {
       email
     }
     Toast.loading("Loading...", 600);          
     getPromocodeByEmail(data).then((res) => {
       Toast.hide();            
       if (res.data.errorCode === "200" && res.data.data) {
         if(res.data.data && res.data.data !== ''){
           message.info('This user already exists.')
           setEmail('')
         }
       }
     })
   }

  return (
    <>
      <DataTable
        className="mt-7"
        rows={rows}
        search={{
          type: "q",
          placeholder: "Search ambassadors",
        }}
        searchQuery={searchFilter}
        onChangeSearch={(value) => setSearchFilter(value)}
        headings={headings}
        sortHeaders={sortHeaders}
        activeSort={activeSort}
        onSortByHeader={(header) => setActiveSort(header)}
        pagination={true}
        perPage={perPage}
        currentPage={currentPage}
        onChangePage={(page) => setCurrentPage(page)}
        totalResults={total}
        selectable={false}
        selection={selection}
        onSelect={(selected) => setSelection(selected)}
        header="Ambassadors"
      />

      <PopupModal
        open={showModal}
        onClose={() => setShowModal(false)}
        title={`Edit ${selectedUser?.username}'s Balance`}
        primaryAction={{
          label: "Save",
          onClick: () => updateCustomerBalanceFunc(),
        }}
        secondaryAction={{
          label: "Cancel",
          onClick: () => setShowModal(false),
        }}
      >
        <Input
          label="Points"
          subLabel={`Current Balance: ${selectedUser?.balance}`}
          type="number"
          placeholder={selectedUser?.balance}
          value={balance}
          onChange={(e) => setBalance(e)}
        />

        <Input
          label={`Write a message for ${selectedUser?.username} (optional)`}
          placeholder={`Write a message for ${selectedUser?.username} (optional)`}
          className={"mt-5"}
          value={msg}
          onChange={(e) => setMessage(e)}
        />
      </PopupModal>

      <PopupModal
        open={showAddPointsModal}
        onClose={() => setShowAddPointsModal(false)}
        title={`Add Points`}
        primaryAction={{
          label: "Save",
          onClick: () => addPointsForCustomerFunc(),
        }}
        secondaryAction={{
          label: "Cancel",
          onClick: () => setShowAddPointsModal(false),
        }}
      >
        <Input
          label="Points"
          subLabel={`Current Balance: ${selectedUser?.balance}`}
          type="number"
          placeholder="Enter points to add"
          value={addPointsValue}
          onChange={(e) => setAddPointsValue(e)}
        />

        <Input
          label={`Email`}
          placeholder={`Enter Email`}
          className={"mt-5"}
          value={email}
          onChange={(e) => setEmail(e)}
          onBlur={(e) => checkEmailUniqueBlur(e)}
        />

        <Input
          label={`First Name`}
          placeholder={`Enter First Name`}
          className={"mt-5"}
          value={addPointsFname}
          onChange={(e) => setAddPointsFname(e)}
        />

        <Input
          label={`Last Name`}
          placeholder={`Enter Last Name`}
          className={"mt-5"}
          value={addPointsLname}
          onChange={(e) => setAddPointsLname(e)}
        />
      </PopupModal>
    </>
  );
}
