import React, { useEffect, useRef, useState } from 'react';

export function Input({ label, subLabel, value, onChange, onBlur, className, placeholder, prefix, type, highlightColor, affix, min, max,classNameI, affixStyle }) {


  const handleChange = (e) => {
    const value = e.target.value;
    onChange(value);
  }

  return (
    <div className={`select w-full ${className}`}>
      <label className=" text-gray-1 font-heading font-bold text-base">{label}</label>

      {subLabel && <span className=' font-body italic text-gray-4 text-xs block'>{subLabel}</span>}

      <div className={`relative w-full font-heading-semi font-semibold text-base ${label ? 'mt-3' : ''}`}>

        {prefix && <div className="absolute left-0 top-0 flex items-center justify-center h-full aspect-square bg-border-color rounded-lg"
          style={highlightColor ? { backgroundColor: highlightColor } : {}}>
          {prefix}
        </div>}

        <input className={`border-border-color border-2 rounded-lg px-4 py-3 w-full text-black-1 ${prefix ? ' pl-16' : ''} ${classNameI}` }
          placeholder={placeholder}
          onChange={handleChange}
          onBlur={onBlur}
          value={value}
          type={type}
          min={min}
          max={max}
          style={highlightColor ? { borderColor: highlightColor } : {}}
        />

        {affix && <div className="affix absolute right-11 top-0 flex items-center justify-center h-full text-gray-1" style={{...affixStyle}}>
          {affix}
        </div>}



      </div>

    </div>
  );
}