import React from 'react'
import './style.scss'
import '../Loyalty/s1output.scss'
import { Breadcrumbs } from "../../containers/Integrations/components/breadcrumbs"
import TopToolbar from '../../components/OverView/TopToolbar'
import { MediaCard } from '../Integrations/components/mediaCard'
import { VideoThumbnail } from '../Integrations/components/videoThumbnail'
import EmailReports from "../../components/AutoEmails/EmailReports"
import IntegrationAndMarketing from './Integration&Marketing'
import Customize from '../../components/AutoEmailsDetails/Customize'
import { useLocation } from 'react-router-dom'

const AutoEmailDetail = () => {
    const location = useLocation()
    const { title } = location.state;

    const handleChange = (e) => {
        console.log(e.target.checked)
    }

    {/* if (Number(id) === 1 || Number(id) === 6) */ }
    return (
        <div className='auto-email-detail loyalty'>
            <TopToolbar
            // vendorList={vendorList}
            // vendorId={currentVendorId}
            // setVendorId={setCurrentVendorId}
            // disabled={bonusId}
            />
            <div className='p-14 w-full'>
                <Breadcrumbs
                    breadcrumbs={[
                        { name: "Auto-Emails", link: "/vendor/auto-email" },
                        { name: `${title}`, link: null }
                    ]}
                    large={false}
                />
                <div className="card">
                    <div className="card-content flex justify-between items-center">
                        <div className='flex justify-between items-center'>
                            {/* <div className={`p-[10px] bg-green-lighter text-green rounded-full w-max mr-4`}>
              {icon}
            </div> */}
                            <span className='font-heading text-2xl font-extrabold font-extra-bold'>{title}</span>
                        </div>

                        <div className='flex items-center'>
                            <span className='font-heading-semi font-semibold text-xl'>Email Status</span>
                            <label className="relative inline-flex items-center cursor-pointer ml-5">
                                <input type="checkbox" className="sr-only peer" checked={true} onChange={handleChange} />
                                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 rounded-full peer peer-checked:after:translate-x-[calc(100%+3px)] peer-checked:after:border-white after:content-[''] after:absolute after:top-[4px] after:left-[4px] after:bg-white after:shadow-lg after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-yellow"></div>
                            </label>

                        </div>
                    </div>
                </div>

                <MediaCard
                    media={
                        <VideoThumbnail
                            poster="https://img.freepik.com/free-photo/smiling-blogger-girl-is-showing-okay-gesture-by-sitting-white-background_176474-117379.jpg?w=1800&t=st=1676366049~exp=1676366649~hmac=dcb198e638d7fb65682568f2a8c9f8eaef5ac799aec4f0a24245f442815affd8"
                            video="https://www.w3schools.com/html/mov_bbb.mp4"
                            aspectRatio="4/3"
                        />
                    }
                    primaryAction={{
                        label: "Get the tips",
                        onClick: () => { }
                    }}
                    secondaryAction={{
                        label: "FAQs for Family and Friends Code Reminder",
                        onClick: () => { }
                    }}
                >
                    <h3 className=" font-heading-semi font-semibold text-xl mb-4">
                        What is it?
                    </h3>
                    <p className="text-gray-1 font-body text-lg leading-7">
                        This automated email is sent to customers after they complete a purchase or place an order. It informs them of the rewards they’ve earned and provides a prompt to claim them.
                    </p>
                    <p className="text-gray-1 font-body text-lg leading-7 mt-6">The email includes a detailed breakdown of rewards per line item, factoring in all applicable multipliers and values, and presents the final total amount of rewards earned.
                    </p>
                </MediaCard>

                {/* <EmailReports /> */}

                <Customize />

                <IntegrationAndMarketing />
            </div>
        </div>
    )
    {/* else
        return <EmailDetailView className='auto-email-detail loyalty'/> */}
}

export default AutoEmailDetail