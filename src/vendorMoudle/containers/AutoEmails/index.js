import React, { useEffect, useState } from "react"
import TopToolbar from "../../components/OverView/TopToolbar"
import "./style.scss"
import GlobalSettings from "../../components/AutoEmails/GlobalSettings"
import EmailTypeIntegration from "../../components/AutoEmails/Emails"
import { use } from "react"
import { getCnstCampaignProducts } from "../../requests/product"
import { Toast } from "antd-mobile"
import { message } from "antd"
import { getGlobalSettings, getEmailTypes } from "../../requests/autoemails"
import EmailReports from "../../components/AutoEmails/EmailReports"

const AutoEmail = () => {
    const [globalSettings, setGlobalSettings] = useState()
    const [emailTypes, setEmailTypes] = useState([])
    const [currentVendorId, setCurrentVendorId] = useState(
        sessionStorage.getItem('loginType') === '0'
            ? sessionStorage.getItem('vendorAdminSelectVendorId')
            : sessionStorage.getItem('vendorId')
    )

    function getCampaignData() {
        if (sessionStorage.getItem('cgpnId') !== null) {
            getData(sessionStorage.getItem('cgpnId'))
            return
        }
        if (sessionStorage.getItem('vendorId') == 0) {
            return
        }

        let data = {
            vendorId: sessionStorage.getItem('vendorId'),
        }
        Toast.loading('Loading...', 600)

        getCnstCampaignProducts(data).then((res) => {
            Toast.hide()
            if (res.data.errorCode === '200') {
                let response = res.data.response
                let campaignCommandList = response.campaignCommandList
                if (campaignCommandList.length > 0) {
                    sessionStorage.setItem('cpgnId', campaignCommandList[0].cpgnId)
                    getData(campaignCommandList[0].cpgnId)
                }
                else {
                    return
                }

            } else {
                message.error(res.data.errorMessage)
            }
        })
    }

    const getData = (cpgnId) => {
        let data = {
            cpgnId,
        }
        Toast.loading("Loading...")
        getGlobalSettings(data).then((res) => {
            Toast.hide()
            if (res.data.errorCode === "200") {
                let response = res.data.data
                setGlobalSettings(response)
            } else {
                message.error(res.data.errorMessage)
            }
        })

        getEmailTypes(data).then((res) => {
            Toast.hide()
            if (res.data.errorCode === "200") {
                let response = res.data.data
                setEmailTypes(response)
            }
            else {
                message.error(res.data.errorMessage)
            }
        })
    }

    useEffect(() => {
        getCampaignData()
    }, [currentVendorId])

        ; (function () {
            const originalSetItem = sessionStorage.setItem
            const originalRemoveItem = sessionStorage.removeItem

            const sessionStorageChangeEvent = new Event('sessionStorageChange')



            sessionStorage.setItem = function (key, value) {
                originalSetItem.apply(this, arguments)
                window.dispatchEvent(sessionStorageChangeEvent)
                if (key === 'vendorAdminSelectVendorId' && currentVendorId !== value)
                    setCurrentVendorId(value)
            }
        })()

    // Usage: Listen for the custom sessionStorage change event
    window.addEventListener('sessionStorageChange', () => { })

    return (
        <div className="autoemail-main-view">
            <TopToolbar
            // vendorList={vendorList}
            // vendorId={currentVendorId}
            // setVendorId={setCurrentVendorId}
            // disabled={bonusId}
            />
            <div className='autoEmail_view'>
                <div className='autoEmail_top_view'>
                    <div className='autoEmail_title'>Auto-Emails</div>
                    <div style={{ flex: 1 }}></div>
                </div>
                <div className='autoEmail_content_view top'>
                    {/* <EmailReports /> */}
                    <GlobalSettings globalSettings={globalSettings} getCampaignData={getCampaignData} />
                    <EmailTypeIntegration emailTypes={emailTypes}/>
                </div>
            </div></div>)

}

export default AutoEmail