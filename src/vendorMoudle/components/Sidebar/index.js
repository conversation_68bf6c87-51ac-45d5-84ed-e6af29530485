/**
 *
 * Sidebar
 *
 */

import React, { useEffect, useRef, useState } from 'react'
import { useHistory } from 'react-router-dom'
import PropTypes from 'prop-types'
import './style.scss'
import { Link, NavLink, withRouter } from 'react-router-dom'
import PermissionsControl from '../../../components/PermissionsControl'
import { DownOutlined } from '@ant-design/icons'
import Ambassadors, {
  AMBASSADOR_STATUS,
  DEFAULT_STATUS,
} from '../../containers/Ambassadors'
import {
  getCustFlagList,
  insertCustFlag,
  deleteCustFlag,
  getVendorInfo,
} from '../../requests/setting'
import { Toast } from 'antd-mobile'
import Pubsub from 'pubsub-js'
import { generateVendorLink } from '../../requests/dashboard'
import ShareSignUpLink from "../../containers/OverViewN/Modal/ShareSignUpLink";
import InviteUsers from '../../containers/OverViewN/Modal/InviteUsers'
import {
  GenerateVendorLink,
  VendorCampaigns,
  submitDataInit,
  submitHandle,
} from '../../containers/Ambassadors/AmbassadorHandle'
import { uploadProductImage } from '../../requests/product'
import { Input, message, Select } from 'antd'
import { getAllBrandlist } from '../../requests'
import logo from '../../images/bonus_upload_icon.png'
import arrow from '../../images/arrow-copy.svg'
import kwiklogo from '../../images/logo.png'

const { Option } = Select

const renderSelectedVendor = (vendor) => (
  <div className='brand-option'>
    <div className='brand-logo'>
      <img src={vendor.logoUrl ?? 'default-logo.png'} alt='' />
    </div>
    <div className='title'>
      {vendor.vname}
      <div className='sub-text'>Business</div>
    </div>
  </div>
)

const BrandDropdownIcon = () => (<div style={{ display: 'flex', flexDirection: 'column' }}>
  <img src={arrow} alt='arrow' className='up-arrow ' height={6} />
  <img src={arrow} alt='arrow' height={6} />
</div>)

function Sidebar(props) {
  const {
    location: { pathname },
    t,
  } = props
  const [currentVendorId, setCurrentVendorId] = useState(
    sessionStorage.getItem('loginType') === '0'
      ? sessionStorage.getItem('vendorAdminSelectVendorId')
      : sessionStorage.getItem('vendorId')
  )
  const history = useHistory()
  const [bonusSelect, setBonusSelect] = useState(true)
  const [lpSelect, setLPSelect] = useState(true)
  const [settingSelect, setSettingSelect] = useState(true)
  const [vendorList, setVendorList] = useState([])
  const [searchValue, setSearchValue] = useState('')
  const [paramVendorId, setParamVendorId] = useState();
  const [generalSearch, setGeneralSearch] = useState('')
  const [selectedVendor, setSelectedVendor] = useState()

  const [showInviteUsesModal, setShowInviteUsesModal] = useState(false)
  const [showSignUpModal, setShowSignUpModal] = useState(false)

  const initSubmitData = {
    campaigns: [],
    vendorLinkKey: '',
    vendorLink: '',
    imageUrl: '',
    status: DEFAULT_STATUS,
    emails: [],
    promoCode: '',
    submitCampaigns: [],
    isSendEmail: true,
  }
  const [submitData, setSubmitData] = useState(initSubmitData)
  const statusList = Object.entries(AMBASSADOR_STATUS).map(([key, value]) => {
    return { id: key * 1, name: value }
  })

  const pub = Pubsub.subscribe('CHANGEVENDOR', (msg, data) => {
    console.info(msg, data)
    setCurrentVendorId(data)
  })

  function signoutClick() {
    // sessionStorage.clear();
    Pubsub.unsubscribe(pub)
    sessionStorage.removeItem('tabIndex')
    sessionStorage.removeItem('loginType')
    sessionStorage.removeItem('vendorAdminSelectVendorId')
    if (
      sessionStorage.getItem('vendorCount') &&
      parseInt(sessionStorage.getItem('vendorCount')) > 1
    ) {
      history.push('/vendor/brands/' + sessionStorage.getItem('vendorCustId'))
    } else {
      window.localStorage.removeItem('userLoginTokenKeyVendor')
      window.localStorage.removeItem('userLoginTokenValueVendor')
      history.push('/vendor')
    }
  }
  // 获取是否为自己url
  function getIsMe(url) {
    let pathname = window.location.pathname
    return url === pathname
  }

  function clickMenuRadio(status, flag) {
    if (status) {
      deleteCustFlagFunc(flag)
    } else {
      insertCustFlagFunc(flag)
    }
  }

  function insertCustFlagFunc(flag) {
    let data = {
      custId: currentVendorId,
      flagId: flag,
    }
    // Toast.loading("Loading...", 600);
    insertCustFlag(data).then((res) => {
      // Toast.hide();
      if (flag == 72) {
        setBonusSelect(true)
      } else if (flag == 73) {
        setLPSelect(true)
      } else if (flag == 74) {
        setSettingSelect(true)
      }
    })
  }

  function deleteCustFlagFunc(flag) {
    let data = {
      custId: currentVendorId,
      flagId: flag,
    }
    // Toast.loading("Loading...", 600);
    deleteCustFlag(data).then((res) => {
      // Toast.hide();
      if (flag == 72) {
        setBonusSelect(false)
      } else if (flag == 73) {
        setLPSelect(false)
      } else if (flag == 74) {
        setSettingSelect(false)
      }
    })
  }

  const handleSignupButtonClick = async () => {
    if (currentVendorId === '0') {
      Toast.info('Please select a vendor', 2)
    } else {
      getCampaignsListFun(currentVendorId)
    }
  }

  function getCampaignsListFun(vendorId) {
    let data = {
      vendorId,
    }
    Toast.loading('Loading...')
    generateVendorLink(data).then((res) => {
      Toast.hide()
      if (res.data.errorCode === '200') {
        history.push(`${res.data.vendorLink}`)
      } else {
        // message.error(res.data.errorMessage)
      }
    })
  }

  const dataInit = () => {
    setSubmitData(submitDataInit(submitData, currentVendorId, currentVendorId))
  }

  const closeInviteUserModal = () => {
    setShowInviteUsesModal(false)
    dataInit()
  }

  const closeSignUpModal = () => {
    setShowSignUpModal(false);
    dataInit();
  };

  const submitDataHandleJustCopy = () => {
    submitHandle(t, submitData, currentVendorId, () => {});
  };

  const submitDataHandle = () => {
    submitHandle(t, submitData, currentVendorId, () => {
      if (showSignUpModal) {
        closeSignUpModal();
      }
      if (showInviteUsesModal) {
        closeInviteUserModal()
      }
    })
  }

  async function prodImageBeforeUpload(file) {
    if (file.size / 1024 / 1024 / 2 > 1) {
      Toast.info('The image must not be larger than 2M', 2)
      return false
    }
    // setIsChange(true);
    let formData = new FormData()
    formData.append('file', file)
    Toast.loading('Loading...', 600)
    uploadProductImage(formData).then((res) => {
      Toast.hide()
      if (res.data.errorCode === '200') {
        let response = res.data.response
        setSubmitData({ ...submitData, imageUrl: response })
      } else {
        message.error(res.data.errorMessage)
      }
    })
  }

  function getCustFlagListFunc() {
    let data = {
      custId: currentVendorId,
    }
    // Toast.loading("Loading...", 600);
    getCustFlagList(data).then((res) => {
      // Toast.hide();
      if (res.data.errorCode == '200') {
        const dataList = res.data.response
        setBonusSelect(dataList.findIndex((d) => d.flagId == 72) === -1)
        setLPSelect(dataList.findIndex((d) => d.flagId == 73) === -1)
        // setSettingSelect(dataList.findIndex((d) => d.flagId == 74) === -1);
      }
    })
  }

  const onBrandChange = (id) => {
    const selected = vendorList.find((vendor) => vendor.vid === id.value)
    if (selected) {
      setSelectedVendor({
        value: selected.vid,
        label: renderSelectedVendor(selected), // Render custom component as label
      })
    }

    sessionStorage.setItem('vendorId', id.value)
    if (sessionStorage.getItem('loginType') === '0')
      sessionStorage.setItem('vendorAdminSelectVendorId', id?.value)
    sessionStorage.setItem('vendorName', id?.title)
  }

  const getVendorList = () => {
    let data = {
      vendorId:
        sessionStorage.getItem('loginType') === '0'
          ? 0
          : sessionStorage.getItem('vendorCustId'),
    }

    getAllBrandlist(data).then((req) => {
      setVendorList(
        req?.data?.data.map((item) => ({
          ...item,
          logoUrl: item.logoUrl
            ? item.logoUrl
            : item.vname === 'Admin - All Vendors'
              ? kwiklogo
              : logo,
        }))
      )
      if (currentVendorId == 0) {
        setSelectedVendor({
          value: 0,
          label: renderSelectedVendor({
            vname: 'Admin - All Vendors',
            logoUrl: kwiklogo,
          }),
        })
      } else {
        const vendorObj = req?.data?.data?.find(
          (item) => item.vid == currentVendorId
        )
        setSelectedVendor({
          value: currentVendorId,
          label: renderSelectedVendor(vendorObj),
        })
      }
    })
  }

  const getModalData = () => {
    dataInit();

    if (paramVendorId === currentVendorId) {
      GenerateVendorLink(
        currentVendorId,
        t,
        submitData.campaigns,
        (response, campaigns) => {
          const key = response.key;
          const vendorLink = response.vendorLink;
          const imageUrl = response.imgUrl ? response.imgUrl : "";
          let originUrl = window.location.origin;
          if (currentVendorId == sessionStorage.getItem("wer1Id")) {
            if (window.location.host == "app.dev.kwik.zone") {
              originUrl = "https://dev.wer1.zone";
            } else if (window.location.host == "app.kwik.com") {
              originUrl = "https://app.wer1.com";
            }
          }
          setSubmitData({
            ...submitData,
            vendorLinkKey: key,
            vendorLink: originUrl + vendorLink,
            imageUrl: "",
            campaigns: campaigns,
            status: response.status ? response.status : DEFAULT_STATUS
          });
        }
      );
    } else {
      setParamVendorId(currentVendorId);
      VendorCampaigns(t, currentVendorId, newCampaigns => {
        GenerateVendorLink(
          currentVendorId,
          t,
          newCampaigns,
          (response, campaigns) => {
            const key = response.key;
            const vendorLink = response.vendorLink;
            const imageUrl = response.imgUrl ? response.imgUrl : "";
            let originUrl = window.location.origin;
            if (currentVendorId == sessionStorage.getItem("wer1Id")) {
              if (window.location.host == "app.dev.kwik.zone") {
                originUrl = "https://dev.wer1.zone";
              } else if (window.location.host == "app.kwik.com") {
                originUrl = "https://app.wer1.com";
              }
            }
            setSubmitData({
              ...submitData,
              campaigns: campaigns,
              vendorLinkKey: key,
              vendorLink: originUrl + vendorLink,
              imageUrl: "",
              status: response.status ? response.status : DEFAULT_STATUS
            });
          }
        );
      });
    }
  };


  ; (function () {
    const originalSetItem = sessionStorage.setItem
    const originalRemoveItem = sessionStorage.removeItem

    const sessionStorageChangeEvent = new Event('sessionStorageChange')

    sessionStorage.setItem = function (key, value) {
      originalSetItem.apply(this, arguments)
      window.dispatchEvent(sessionStorageChangeEvent)
      if (key === 'vendorAdminSelectVendorId' && currentVendorId !== value)
        setCurrentVendorId(value)
    }
  })()

  // Usage: Listen for the custom sessionStorage change event
  window.addEventListener('sessionStorageChange', () => { })


  useEffect(() => {
    getCustFlagListFunc()
    /* eslint-disable react-hooks/exhaustive-deps */
  }, [currentVendorId])

  useEffect(() => {
    getVendorList()
  }, [currentVendorId])

  const filteredVendors = vendorList.filter((vendor) =>
    vendor.vname.toLowerCase().includes(searchValue.trim().toLowerCase())
  )


  return (
    <div className='sidebar'>
      <div className='logo-section'>
        {/* <Link className="logo" to="/vendor/dashboard">
          <img src={require('../../images/logo.png')} alt="Logo" />
          <h3>Kwikclick</h3>
        </Link> */}
        <img src={require('../../../images/Kwik logo Official.svg')} alt='' />
      </div>
      <div className='menu-section'>
        <div className='menus'>
          {/* <p>{t('SIDEBAR_MENU')}</p> */}
          <ul className='main-menu'>
            <div className='custom-search-option general-search'>
              <img
                src={require('./../../images/search_icon.svg')}
                alt=''
                style={{ cursor: 'pointer' }}
              />
              <Input
                placeholder='Have questions?'
                value={generalSearch}
                onChange={(e) => {
                  setGeneralSearch(e.target.value)
                }}
                onKeyDown={(event) => {
                  if (event.key === "Enter" && generalSearch !== '') {
                    console.log("Enter key pressed!", generalSearch)
                    window.open(`https://help.kwik.com/en/support/search?term=${generalSearch}`, "_blank")
                  }
                }}
                style={{ width: '90%' }}
              />
            </div>
            <div className='outerText'>GENERAL</div>
            <PermissionsControl name='Preview Page'>
              <li>
                <NavLink
                  to='/vendor/dashboard'
                  isActive={() => pathname.split('/').includes('dashboard')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img
                      src={require('./../../images/overview.svg')}
                      alt=''
                    />{' '}
                    <span>{t('SIDEBAR_OVERVIEW')}</span>
                  </div>
                </NavLink>
              </li>
            </PermissionsControl>

            <div className='outerText'>DATA</div>
            {/* {sessionStorage.getItem('vendorCustId') === '0' && (
              <PermissionsControl name='View Customers'>
                <li>
                  <NavLink
                    to='/vendor/customer'
                    isActive={() => pathname.split('/').includes('customer')}
                  >
                    <div className='menu'>
                      <div className={'radio-view noborder'}></div>
                      <img
                        src={require('./../../images/customersIcon.svg')}
                        alt=''
                      />{' '}
                      <span>{t('SIDEBAR_CUSTOMERS')}</span>
                    </div>
                  </NavLink>
          
                </li>
              </PermissionsControl>
            )} */}
            <PermissionsControl name='Product Overview'>
              <li>
                <NavLink
                  to='/vendor/product'
                  isActive={() =>
                    [
                      '/vendor/product',
                      '/vendor/product/edit',
                      '/vendor/products',
                      '/vendor/inventory',
                      '/vendor/product/detail',
                      '/vendor/collection',
                      '/vendor/collection/add',
                      '/vendor/gift-and-promo',
                      '/vendor/gift/add',
                      '/vendor/promocode/add',
                      '/vendor/influencers',
                      '/vendor/campaign-details',
                      '/vendor/warehouse',
                      '/vendor/warehouse/detail',
                      '/vendor/product/copy',
                    ].includes(pathname)
                  }
                  className='hideBg'
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img
                      src={require('./../../images/products.svg')}
                      alt=''
                    />{' '}
                    <span>{t('SIDEBAR_PRODUCTS')}</span>
                  </div>
                </NavLink>
                <div className='inner-menu'>
                  <ul>
                    <li>
                      <NavLink to='/vendor/product' exact>
                        {t('SIDEBAR_OVERVIEW')}
                      </NavLink>
                    </li>
                    <PermissionsControl name='Add Product'>
                      <li>
                        <NavLink to='/vendor/product/detail' exact>
                          {t('SIDEBAR_ADD_PRODUCT')}
                        </NavLink>
                      </li>
                    </PermissionsControl>
                    <PermissionsControl name='Warehouse'>
                      <li>
                        <NavLink to='/vendor/warehouse'>
                          {t('SIDEBAR_WAREHOUSE')}
                        </NavLink>
                      </li>
                    </PermissionsControl>
                    <PermissionsControl name='Inventory'>
                      <li>
                        <NavLink to='/vendor/inventory'>
                          {t('SIDEBAR_INVENTORY')}
                        </NavLink>
                      </li>
                    </PermissionsControl>
                    {/* <li>
                    <NavLink to="/vendor/collection">Collections</NavLink>
                  </li>
                  <li>
                    <NavLink to="/vendor/gift-and-promo" isActive={() => ['/vendor/gift/add', '/vendor/promocode/add', '/vendor/gift-and-promo'].includes(pathname)}>Gift Cards and Promo</NavLink>
                  </li>
                  <li>
                    <NavLink to="/vendor/influencers" isActive={() => ['/vendor/campaign-details', '/vendor/influencers'].includes(pathname)}>
                      Influencer Collaberators
                    </NavLink>
                  </li> */}
                  </ul>
                </div>
              </li>
            </PermissionsControl>
            <PermissionsControl name='View Campaigns'>
              <li>
                <NavLink
                  to='/vendor/reports'
                  isActive={() => pathname.split('/').includes('reports')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img
                      src={require('./../../images/reports.svg')}
                      alt=''
                    />{' '}
                    <span>Reports</span>
                  </div>
                </NavLink>
                {/*<div className="inner-menu">*/}
                {/*  <ul>*/}
                {/*    <li>*/}
                {/*      <NavLink to="/vendor/reports/graph">{t('SIDEBAR_GRAPH_VIEW')}</NavLink>*/}
                {/*    </li>*/}
                {/*  </ul>*/}
                {/*</div>*/}
              </li>
            </PermissionsControl>

            <PermissionsControl name='View Ambassadors'>
              <li>
                <NavLink
                  to='/vendor/ambassadors'
                  isActive={() => pathname.split('/').includes('ambassadors')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img
                      src={require('./../../images/ambassadorsIcon.svg')}
                      alt=''
                    />{' '}
                    <span>{t('SIDEBAR_AMBASSADORS')}</span>
                  </div>
                </NavLink>
              </li>
            </PermissionsControl>

            <PermissionsControl name='View Orders'>
              <li>
                <NavLink
                  to='/vendor/order?option=true'
                  isActive={() => pathname.split('/').includes('order')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img
                      src={require('./../../images/orderIcon.svg')}
                      alt=''
                    />{' '}
                    <span>{t('SIDEBAR_ORDERS')}</span>
                  </div>
                </NavLink>
              </li>
            </PermissionsControl>
            <div className='outerText'>INCENTIVES</div>
            <PermissionsControl name='View Finance'>
              <li>
                <NavLink
                  to='/vendor/commissions'
                  isActive={() => pathname.split('/').includes('commissions')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img src={require('./../../images/Frame.svg')} alt='' style={{ filter: "grayscale(100%)"}}/>{' '}
                    <span>Commissions</span>
                  </div>
                </NavLink>
              </li>
            </PermissionsControl>
            {sessionStorage.getItem('loginType') === '0' || lpSelect ? (
              <PermissionsControl name='View Loyalty'>
                <li>
                  {/* {sessionStorage.getItem("loginType") === "0" ? (
                      <div
                        className={lpSelect ? "radio-view focus" : "radio-view"}
                        onClick={(e) => clickMenuRadio(lpSelect, 73)}
                      >
                        <img
                          className="menu_radio"
                          src={require("../../images/radio-check.png")}
                        />
                      </div>
                    ) : null} */}
                  <NavLink
                    to='/vendor/loyalty'
                    isActive={() =>
                      ['/vendor/loyalty', '/vendor/loyalty/create'].includes(
                        pathname
                      )
                    }
                    className='hideBg'
                  >
                    <div className='menu'>
                      {/* {sessionStorage.getItem("loginType") !== "0" ? (
                          <div className={"radio-view noborder"}></div>
                        ) : null} */}
                      <div className={'radio-view noborder'}></div>
                      <img
                        src={require('./../../../images/icons/loyalty.svg')}
                        alt=''
                      />{' '}
                      <span>{t('SIDEBAR_LOYALTY')}</span>
                    </div>
                  </NavLink>

                  <div className='inner-menu'>
                    <ul>
                      <li>
                        <NavLink to='/vendor/loyalty' exact>
                          {t('SIDEBAR_OVERVIEW')}
                        </NavLink>
                      </li>
                      <PermissionsControl name='Add a Point'>
                        <li>
                          <NavLink to='/vendor/loyalty/create' exact>
                            {t('SIDEBAR_ADD_LOYALTY_POINT')}
                          </NavLink>
                        </li>
                      </PermissionsControl>
                    </ul>
                  </div>
                </li>
              </PermissionsControl>
            ) : null}

            {/* {sessionStorage.getItem('loginType') === '0' || bonusSelect ? (
              <PermissionsControl name='View Bonus'>
                <li>
                 
                  <NavLink
                    to='/vendor/bonus'
                    isActive={() =>
                      ['/vendor/bonus', '/vendor/bonus/edit'].includes(pathname)
                    }
                    className='hideBg'
                  >
                    <div className='menu'>
                      <div className={'radio-view noborder'}></div>
                      <img
                        src={require('./../../images/hugeicons-gift.svg')}
                        alt=''
                      />{' '}
                      <span>{t('SIDEBAR_BONUS')}</span>
                    </div>
                  </NavLink>

                  <div className='inner-menu'>
                    <ul>
                      <li>
                        <NavLink to='/vendor/bonus' exact>
                          {t('SIDEBAR_OVERVIEW')}
                        </NavLink>
                      </li>
                      <PermissionsControl name='Add Bonus'>
                        <li>
                          <NavLink to='/vendor/bonus/edit' exact>
                            {t('SIDEBAR_ADD_BONUS')}
                          </NavLink>
                        </li>
                      </PermissionsControl>
                    </ul>
                  </div>
                </li>
              </PermissionsControl>
            ) : null} */}

            <PermissionsControl name='View Finance'>
              <li>
                <NavLink
                  to='/vendor/cashback'
                  isActive={() => pathname.split('/').includes('cashback')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img src={require('./../../images/money-back.svg')} alt='' style={{ filter: "grayscale(100%)"}}/>{' '}
                    <span>Cashback</span>
                  </div>
                </NavLink>
              </li>
            </PermissionsControl>

            <div className='outerText'>ADMIN</div>
            <PermissionsControl name='View Campaigns'>
              <li>
                <NavLink
                  to='/vendor/campaigns'
                  isActive={() =>
                    [
                      '/vendor/campaigns',
                      '/vendor/campaigns/detail',
                      '/vendor/campaignsproducts',
                    ].includes(pathname)
                  }
                  className={getIsMe('/vendor/campaigns') ? '' : 'hideBg'}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img
                      src={require('./../../images/campaignsIcon.svg')}
                      alt=''
                    />{' '}
                    <span>{t('SIDEBAR_CAMPAIGNS')}</span>
                  </div>
                </NavLink>
                <div className='inner-menu'>
                  <ul>
                    <PermissionsControl name='Add Campaign'>
                      <li>
                        <NavLink to='/vendor/campaigns/detail'>
                          {t('SIDEBAR_CREATE_EDIT_CAMPAIGNS')}
                        </NavLink>
                      </li>
                    </PermissionsControl>
                    <PermissionsControl name='Map Campaign/Products'>
                      <li>
                        <NavLink to='/vendor/campaignsproducts'>
                          {t('SIDEBAR_MAP_CAMPAIGNS_PRODUCTS')}
                        </NavLink>
                      </li>
                    </PermissionsControl>
                  </ul>
                </div>
              </li>
            </PermissionsControl>
            {/* <PermissionsControl name='View Finance'>
              <li>
                <NavLink
                  to='/vendor/finance'
                  isActive={() => pathname.split('/').includes('finance')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img
                      src={require('./../../images/marketing.svg')}
                      alt=''
                    />{' '}
                    <span>Finance</span>
                  </div>
                </NavLink>
              </li>
            </PermissionsControl> */}

            <li>
              {/* <NavLink
                to="/vendor/marketing"
                isActive={() => pathname.split('/').includes('marketing')}
              >
                <div className="menu">
                  <img src={require('./../../images/marketing.svg')} alt="" />{' '}
                  <span>Marketing</span>
                </div>
              </NavLink> */}
              <div className='inner-menu'>
                <ul>
                  <li>
                    <NavLink to='/vendor/marketing/whatsapp'>WhatsApp</NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/marketing/facebook'>Facebook</NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/marketing/instagram'>
                      Instagram
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/marketing/youtube'>Youtube</NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/marketing/twitter'>Twitter</NavLink>
                  </li>
                </ul>
              </div>
            </li>

            {/* <li>
              <NavLink
                to="/vendor/payouts"
                isActive={() => ['/payouts'].includes(pathname)}
              >
                <div className="menu">
                  <img src={require('./../../images/marketing.svg')} alt="" />{' '}
                  <span>{t('SIDEBAR_PAYOUTS')}</span>
                </div>
              </NavLink>
            </li> */}
            {/* <li>
              <NavLink
                to="/vendor/staff"
                isActive={() => pathname.split('/').includes('staff')}
              >
                <div className="menu">
                  <img src={require('./../../images/marketing.svg')} alt="" />{' '}
                  <span>{t('SIDEBAR_STAFF_MANAGEMENT')}</span>
                </div>
              </NavLink>
            </li> */}

            {sessionStorage.getItem('loginType') === '0' || settingSelect ? (
              <PermissionsControl name='View Setting'>
                <li>
                  {/* <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      width: '100%',
                    }}
                  > */}
                    {/* {sessionStorage.getItem("loginType") === "0" ? (
                      <div
                        className={
                          settingSelect ? "radio-view focus" : "radio-view"
                        }
                        onClick={(e) => clickMenuRadio(settingSelect, 74)}
                      >
                        <img
                          className="menu_radio"
                          src={require("../../images/radio-check.png")}
                        />
                      </div>
                    ) : null} */}
                    <NavLink
                      to='/vendor/setting'
                      isActive={() =>
                        ['/vendor/setting', '/vendor/user/detail'].includes(
                          pathname
                        )
                      }
                    >
                      <div className='menu'>
                        <div className={'radio-view noborder'}></div>
                        <img
                          src={require('./../../images/setting.svg')}
                          alt=''
                        />{' '}
                        <span>{t('SIDEBAR_SETTINGS')}</span>
                      </div>
                    </NavLink>
                  {/* </div> */}
                </li>
              </PermissionsControl>
            ) : null}

            <div className='outerText'>MARKETING</div>
            <PermissionsControl name='View Finance'>
              <li>
                <NavLink
                  to='/vendor/auto-email'
                  isActive={() => pathname.split('/').includes('cashback')}
                >
                  <div className='menu'>
                    <div className={'radio-view noborder'}></div>
                    <img src={require('./../../images/money-back.svg')} alt='' style={{ filter: "grayscale(100%)"}}/>{' '}
                    <span>Auto-Emails</span>
                  </div>
                </NavLink>
              </li>
            </PermissionsControl>

            <InviteUsers
              showInviteUsesModal={showInviteUsesModal}
              modalClose={closeInviteUserModal}
              submitHandle={submitDataHandle}
              submitData={submitData}
              setSubmitData={setSubmitData}
              prodImageBeforeUpload={prodImageBeforeUpload}
              statusList={statusList}
            />

            <li
              style={{
                display: 'none',
              }}
            >
              <NavLink
                to='/vendor/integrations/general'
                isActive={() =>
                  [
                    '/vendor/integrations',
                    '/vendor/integrations/general',
                    '/vendor/integrations/cashback',
                    '/vendor/integrations/widget',
                    '/vendor/integrations/co-branded-signup',
                    '/vendor/integrations/platform-welcome',
                    '/vendor/integrations/post-purchase',
                    '/vendor/integrations/order-confirmation',
                    '/vendor/integrations/custom-landing',
                    '/vendor/integrations/ghost-app',
                    '/vendor/integrations/utm',
                  ].includes(pathname)
                }
              >
                <div className='menu'>
                  <img
                    src={require('./../../../images/icons/integration.svg')}
                    alt=''
                  />{' '}
                  <span>{t('SIDEBAR_INTEGRATIONS')}</span>
                </div>
              </NavLink>
              <div className='inner-menu'>
                <ul>
                  <li>
                    <NavLink to='/vendor/integrations/general'>
                      {t('SIDEBAR_INTEGRATIONS_GENERAL')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/cashback'>
                      {t('SIDEBAR_INTEGRATIONS_CASHBACK')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/widget'>
                      {t('SIDEBAR_INTEGRATIONS_WIDGET')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/co-branded-signup'>
                      {t('SIDEBAR_INTEGRATIONS_CO_BRANDED_SIGNUP')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/platform-welcome'>
                      {t('SIDEBAR_INTEGRATIONS_PLATFORM_WELCOME')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/post-purchase'>
                      {t('SIDEBAR_INTEGRATIONS_POST_PURCHASE')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/order-confirmation'>
                      {t('SIDEBAR_INTEGRATIONS_ORDER_CONFIRMATION')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/custom-landing'>
                      {t('SIDEBAR_INTEGRATIONS_CUSTOM_LANDING')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/ghost-app'>
                      {t('SIDEBAR_INTEGRATIONS_GHOST_APP')}
                    </NavLink>
                  </li>
                  <li>
                    <NavLink to='/vendor/integrations/utm'>
                      {t('SIDEBAR_INTEGRATIONS_UTM')}
                    </NavLink>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
          {/* <p>Insights</p>
          <ul>
            <li>
              <NavLink
                to="/vendor/inbox"
                isActive={() => ['/inbox'].includes(pathname)}
              >
                <div className="menu">
                  <img src={require('./../../images/inbox.svg')} alt="" />{' '}
                  <span>Inbox</span>
                  <span className="badge">18</span>
                </div>
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/vendor/notifications"
                isActive={() => ['/notifications'].includes(pathname)}
              >
                <div className="menu">
                  <img
                    src={require('./../../images/notifications.svg')}
                    alt=""
                  />{' '}
                  <span>Notifications</span>
                  <span className="badge">2</span>
                </div>
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/vendor/comments"
                isActive={() => ['/comments'].includes(pathname)}
              >
                <div className="menu">
                  <img src={require('./../../images/comments.svg')} alt="" />{' '}
                  <span>Comments</span>
                  <span className="badge">30</span>
                </div>
              </NavLink>
            </li>
          </ul> */}
        </div>
      </div>

      {/* <div className='account-details'>
        <div className='user-logo'>
          <img
            src={require('./../../images/user-avatar.jpg')}
            alt='User Avatar'
          />
        </div>
        <div className='user-name'>
          <p>{sessionStorage.getItem('vendorName')}</p>
        </div> */}
      {/* <i className="fa fa-angle-down" /> */}
      {/* <div className="downIcon">
          <DownOutlined />
        </div> */}
      {/* </div> */}
      <div className='bottom-options'>
        <ul>  
          <li>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                  marginTop: '15px',
                }}
              >
                <a
                  href='https://help.kwik.com/en/support/home'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  <div className='menu bottom-menu'>
                    <div className={'radio-view noborder'}></div>
                    <span>Contact Support</span>
                  </div>
                </a>
              </div>
            </li>

            <li>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                <NavLink to='#' onClick={() => {
                  getModalData()
                  setShowSignUpModal(true)}
                  } isActive={() => false}>
                  <div className='menu bottom-menu'>
                    <div className={'radio-view noborder'}></div>
                    <span>Sign Up Page</span>
                  </div>
                </NavLink>
              </div>
            </li>
            <ShareSignUpLink
              showSignUpModal={showSignUpModal}
              modalClose={closeSignUpModal}
              submitHandle={submitDataHandle}
              submitData={submitData}
              setSubmitData={setSubmitData}
              submitDataHandleJustCopy={submitDataHandleJustCopy}
              prodImageBeforeUpload={prodImageBeforeUpload}
              statusList={statusList}
            />
            <li>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: '100%',
                }}
              >
                <NavLink to='#' onClick={() => {
                  getModalData()
                  setShowInviteUsesModal(true)}
                  } isActive={() => false}>
                  <div className='menu bottom-menu'>
                    <div className={'radio-view noborder'}></div>
                    <span>Add Ambassador</span>
                  </div>
                </NavLink>
              </div>
            </li>
        </ul>
        <div className='vendor-list'>
        {vendorList?.length > 0 && (
          <Select
            placeholder='Vendor'
            value={selectedVendor}
            labelInValue
            onChange={(e) => {
              onBrandChange(e)
            }}
            filterOption={(input, option) =>
              option.children?.toLowerCase().includes(searchValue.toLowerCase())
            }
            suffixIcon={<BrandDropdownIcon />}
            className='vendor-selection'
            dropdownRender={(menu) => (
              <>
                {menu}
                <div className='custom-search-option'>
                  <img
                    src={require('./../../images/search_icon.svg')}
                    alt=''
                    style={{ cursor: 'pointer' }}
                  />
                  <Input
                    ref={el => { setTimeout(() => el?.focus(), 0) }}
                    placeholder='Search a brand'
                    value={searchValue}
                    autoFocus
                    onChange={(e) => {
                      setSearchValue(e.target.value) // Triggers the Select's filtering
                    }}
                    style={{ width: '90%' }}
                  />
                </div>
              </>
            )}
            getPopupContainer={(triggerNode) => document.body}
          >
            {filteredVendors.map((vendor) => {
              return (
                <Option
                  key={vendor.vid}
                  value={vendor.vid}
                  label={vendor.vname}
                  data-logo-url={vendor.logoUrl}
                  title={vendor.vname}
                >
                  {renderSelectedVendor(vendor)}
                </Option>
              )
            })}
          </Select>
        )}
      </div>
      </div>
      
      {/* <div className='signoutCont'>
        <span onClick={signoutClick}>{t('SIDEBAR_SIGNOUT')}</span>
      </div> */}
    </div>
  )
}

Sidebar.propTypes = {
  location: PropTypes.object,
}

export default withRouter(Sidebar)
