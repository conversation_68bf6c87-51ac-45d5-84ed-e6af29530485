import qs from "qs";
import service from "../../utils/loyaltyService";
// import service from "../../utils/service"

export const getLoyaltyPoints = (data) => {
  return service.get(`/loyalty-points`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointStatistics = (data) => {
  return service.get(`/loyalty-point/statistics`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointTransactions = (data) => {
  return service.get(`/transactions`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointCustomers = (data) => {
  return service.get(`/loyalty-point/customers`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPoint = (data) => {
  return service.get(`/loyalty-point`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyRedeemedDiscount = (data) => {
  return service.get(`/loyalty-point/redeem/dynamic-discount`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointEarnRedeemSettings = (data) => {
  return service.get(`/loyalty-point/earn-redeem-settings-skus`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const createLoyaltyPoint = (data) => {
  return service.post(`/loyalty-point`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const updateLoyaltyPoint = (data) => {
  return service.put(`/loyalty-point`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const updateCustomerBalance = (data) => {
  return service.put(`/loyalty-point/customer-balance`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

  export const addLpForCust = (data) => {
    return service.post("/addLpForCust", data, {
      headers: {
        token: sessionStorage.getItem("vendorToken"),
      },
    });
  };

// Earn endpoints

export const getLoyaltyPointEarnAnniversary = (data) => {
  return service.get(`/loyalty-point/earn/anniversary`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointEarnAnniversary = (data) => {
  return service.post(`/loyalty-point/earn/anniversary`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointEarnBirthday = (data) => {
  return service.get(`/loyalty-point/earn/birthday`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointEarnBirthday = (data) => {
  return service.post(`/loyalty-point/earn/birthday`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointEarnReferral = (data) => {
  return service.get(`/loyalty-point/earn/refer`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointEarnReferral = (data) => {
  return service.post(`/loyalty-point/earn/refer`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointEarnRegistration = (data) => {
  return service.get(`/loyalty-point/earn/registration`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointEarnRegistration = (data) => {
  return service.post(`/loyalty-point/earn/registration`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointEarnReview = (data) => {
  return service.get(`/loyalty-point/earn/review`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointEarnReview = (data) => {
  return service.post(`/loyalty-point/earn/review`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointEarnBuyProduct = (data) => {
  return service.get(`/loyalty-point/earn/buy-product`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointEarnPurchaseMultiplier = (data) => {
  return service.get(`/loyalty-point/earn/purchase-multiplier`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const deleteLoyaltyW2ePurchaseMultp = (data) => {
  return service.delete(`/deleteLoyaltyW2ePurchaseMultp`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const deleteLoyaltyW2r = (data) => {
  return service.delete(`/deleteLoyaltyW2r`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};  

export const saveLoyaltyPointEarnPurchaseMultiplier = (data) => {
  return service.post(`/loyalty-point/earn/purchase-multiplier`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointEarnBuyProduct = (data) => {
  return service.post(`/loyalty-point/earn/buy-product`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

// Redeem endpoints

export const getLoyaltyPointRedeemFreeProduct = (data) => {
  return service.get(`/loyalty-point/redeem/free-product`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointRedeemFreeProduct = (data) => {
  return service.post(`/loyalty-point/redeem/free-product`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointRedeemFreeShipping = (data) => {
  return service.get(`/loyalty-point/redeem/free-shipping`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointRedeemFreeShipping = (data) => {
  return service.post(`/loyalty-point/redeem/free-shipping`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointRedeemDiscountAbsolute = (data) => {
  return service.get(`/loyalty-point/redeem/discount-absolute`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointRedeemDiscountAbsolute = (data) => {
  return service.post(`/loyalty-point/redeem/discount-absolute`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointRedeemDiscountRelative = (data) => {
  return service.get(`/loyalty-point/redeem/discount-relative`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};
export const saveLoyaltyPointRedeemDiscountRelative = (data) => {
  return service.post(`/loyalty-point/redeem/discount-relative`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointRedeemRepeatPurchase = (data) => {
  return service.get(`/loyalty-point/redeem/repeat-purchase`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const saveLoyaltyPointRedeemRepeatPurchase = (data) => {
  return service.post(`/loyalty-point/redeem/repeat-purchase`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    }
  })
} 

export const saveLoyaltyPointVisitAPage = (data) => {
  return service.post(`/loyalty-point/earn/visit-any`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    }
  })
} 

export const saveLoyaltyPointSubscribeSms = (data) => {
  return service.post(`/loyalty-point/earn/subscribe-sms`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    }
  })
}

export const saveRedeemedDynamicDiscount = (data) => {
  return service.post(`/loyalty-point/redeem/dynamic-discount`, data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    }
  })
} 

export const getLoyaltyPointvisitXPage = (data) => {
  return service.get(`/loyalty-point/earn/visit-xpage`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointsubscribeToSms = (data) => {
  return service.get(`/loyalty-point/earn/subscribe-sms`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointVisitInstagramPage = (data) => {
  return service.get(`/loyalty-point/earn/visit-instagram`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointVisitFacebookPage = (data) => {
  return service.get(`/loyalty-point/earn/visit-facebook`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getLoyaltyPointVisitYoutubePage = (data) => {
  return service.get(`/loyalty-point/earn/visit-youtube`, {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getFileManager = (data) => {
  return service.get("/file-manager", {
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

// 获取产品列表
export const getProdListNewEarn = (data) => {
  return service.post("/getW2eProductList", data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getW2eProductListByTags = (data) => {
  return service.post("/getW2eProductListByTags", data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};


export const getW2eProductListByCollections = (data) => {
  return service.post("/getW2eProductListByCollections", data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

// 获取产品列表
export const getProdListNewR = (data) => {
  return service.post("/getW2rProductList", data, {
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};

export const getAmbLpHistory = (data) => {
  return service.get("/getAmbLpHistoryTx",{
    params: data,
    headers: {
      token: sessionStorage.getItem("vendorToken"),
    },
  });
};